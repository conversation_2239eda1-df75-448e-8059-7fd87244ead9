import { BadRequestException, forwardRef, Inject, Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { EUserContext } from '@superawesome/freekws-auth-library';
import { ParentState } from '@superawesome/freekws-classic-wrapper-common';
import { Tiso31662 } from '@superawesome/freekws-regional-config';
import * as crypto from 'crypto';
import { pbkdf2Sync } from 'crypto';
import { Span } from 'nestjs-ddtrace';
import { FindOptionsWhere, IsNull, Raw, Repository } from 'typeorm';

import { Activation } from './activation.entity';
import { UserCreateParams, UserUpdateFields, UserUpdateParams } from './types';
import { UserRegisterDTO } from './user.dto';
import { User } from './user.entity';
import { App } from '../app/app.entity';
import { AppService } from '../app/app.service';
import { AgeGateService } from '../common/services/age-gate/age-gate.service';
import { AnalyticService } from '../common/services/analytic/analytic.service';
import { FamilyGroupService } from '../common/services/family-group/family-group.service';
import { PreVerificationService } from '../common/services/pre-verification/pre-verification.service';
import { SettingsService } from '../common/services/settings/settings.service';
import { OrgEnv } from '../org-env/org-env.entity';
import { IClientCredentials } from '../org-env/types';
import { yearsSince } from '../utils';
import { WebhookService } from '../webhook/webhook.service';

@Injectable()
@Span()
export class UserService {
  constructor(
    @InjectRepository(User) private readonly userRepo: Repository<User>,
    @InjectRepository(Activation) private readonly activationRepo: Repository<Activation>,
    @InjectRepository(App) private readonly appRepo: Repository<App>,
    @Inject(forwardRef(() => AppService)) private readonly appService: AppService,
    private readonly webhookService: WebhookService,
    private readonly familyGroupService: FamilyGroupService,
    private readonly preVerificationService: PreVerificationService,
    private readonly settingsService: SettingsService,
    private readonly analyticsService: AnalyticService,
    private readonly ageGateService: AgeGateService,
  ) {}

  async create({ orgEnvId, appId, dateOfBirth, language, signUpCountry }: UserCreateParams) {
    const user = new User();
    user.orgEnv = { id: orgEnvId } as OrgEnv;
    user.orgEnvId = orgEnvId;
    user.dateOfBirth = new Date(dateOfBirth);
    user.language = language;
    user.signUpCountry = signUpCountry;

    const saveUserPromise = this.userRepo.save(user);
    const findAppPromise = this.appRepo.findOneOrFail({
      where: { id: appId, orgEnvId },
    });

    const [savedUser, app] = await Promise.all([saveUserPromise, findAppPromise]);

    const activation = this.activationRepo.create({
      user: savedUser,
      app: app,
    });

    const saveActivationPromise = this.activationRepo.save(activation);
    const webhookPromise = this.webhookService.sendChildActivated(
        {
          userId: savedUser.id,
          username: savedUser.username,
          language,
          signUpCountry: signUpCountry ?? '',
          dateOfBirth,
        },
        appId,
        orgEnvId,
    );

    await Promise.all([saveActivationPromise, webhookPromise]);

    return savedUser;
  }


  async getById(orgEnvId: string, id: number): Promise<User | null> {
    return await this.userRepo.findOneBy({ id, orgEnvId });
  }

  async updateWithMissingValues(orgEnvId: string, id: number, update: UserUpdateParams = {}) {
    let hasChanges = false;
    const query: FindOptionsWhere<User> = Object.entries(update)
      .filter(([key]) => UserUpdateFields.includes(key as keyof UserUpdateParams))
      .reduce(
        (acc, [key, value]) => {
          if (!value) {
            return acc;
          }
          hasChanges = true;
          return { ...acc, [key]: IsNull() };
        },
        { id, orgEnvId } as FindOptionsWhere<User>,
      );

    hasChanges && (await this.userRepo.update(query, update));
  }

  async updateDateOfBirth(orgEnvId: string, id: number, dateOfBirth: Date) {
    const { affected } = await this.userRepo.update({ id, orgEnvId }, { dateOfBirth });
    return !!affected;
  }

  async getParentEmail(userId: number) {
    const guardianLinks = await this.familyGroupService.getGuardianLinks(userId);

    if (guardianLinks.confirmedRequests.length > 0) {
      return guardianLinks.confirmedRequests[0].guardian.email;
    }

    if (guardianLinks.pendingRequests.length > 0) {
      return guardianLinks.pendingRequests[0].email;
    }
  }

  async getParentState(parentEmail: string, orgId: string): Promise<ParentState> {
    const verifications = await this.preVerificationService.getVerificationsByEmail({
      email: parentEmail,
      orgId,
    });

    const verification = verifications.find((verification) => verification.userContext === EUserContext.Parent);
    if (!verification) {
      return this.getParentStateAllFalse();
    }

    return {
      verified: false, // deprecated field. It does not mean that parent is VPC verified. It actually used to mean that the parent accepted the child
      idVerified: Boolean(verification), // the only used field. It indicates if parent completed VPC verification
      deleted: false, // deprecated field
      rejected: false, // deprecated field
      expired: false, // deprecated field
    };
  }

  getParentStateAllFalse(): ParentState {
    return {
      verified: false,
      idVerified: false,
      deleted: false,
      rejected: false,
      expired: false,
    };
  }

  async deleteActivation(orgEnvId: string, userId: number, appId: number) {
    const app = await this.appRepo.findOneOrFail({
      relations: { orgEnv: true },
      where: { id: appId, orgEnvId },
    });

    const credentials = {
      clientId: app.orgEnv.clientId,
      secret: app.orgEnv.clientSecret,
    };
    const productId = app.productId;
    const activation = await this.activationRepo.findOneOrFail({
      relations: {
        user: true,
      },
      where: {
        user: { id: userId, orgEnv: { id: orgEnvId } },
        app: { id: appId },
        orgEnvId,
      },
    });

    const activationCount = await this.activationRepo.count({
      where: { user: { id: userId, orgEnv: { id: orgEnvId } }, orgEnvId },
    });
    if (activationCount === 1) {
      await this.deleteUser(orgEnvId, userId, credentials);
    } else {
      await this.settingsService.deleteUserSettingsAtProductLevel(userId, productId, [], credentials);
      await this.activationRepo.delete(activation);

      await this.webhookService.sendChildActivationDeleted(
        {
          dateOfBirth: activation.user.dateOfBirth?.toString() ?? '',
          language: activation.user.language,
          signUpCountry: activation.user.signUpCountry ?? '',
          userId: activation.user.id,
          username: activation.user.username,
        },
        appId,
        orgEnvId,
      );
    }
  }

  async deleteUser(orgEnvId: string, userId: number, credentials: IClientCredentials) {
    await this.settingsService.deleteUser(userId, credentials);
    const user = await this.userRepo.findOneOrFail({
      where: { id: userId, orgEnvId },
    });
    await this.userRepo.delete({ id: userId });
    const orgId = user.orgEnvId;

    await this.webhookService.sendUserAccountDeleted(
      {
        userId: userId,
      },
      orgId,
    );
  }

  async getUserActivation(orgEnvId: string, userId: number, appId: number) {
    return await this.activationRepo.findOne({
      where: { user: { id: userId, orgEnv: { id: orgEnvId } }, app: { id: appId }, orgEnvId },
    });
  }

  async activateUserToApp(
    userId: number,
    appId: number,
    appActivationRequest: {
      permissions?: string[] | undefined;
      parentEmail?: string;
    },
    orgEnvId: string,
  ) {
    const { app } = await this.appService.getAppInfoBy(appId, orgEnvId);
    const user = await this.userRepo.findOneOrFail({
      where: { id: userId, orgEnvId: app.orgEnvId ?? -1 },
    });

    const { permissions } = appActivationRequest;
    let activationPermissions: Record<string, boolean | null> = {};
    if (permissions && permissions.length > 0) {
      const parentEmail = appActivationRequest.parentEmail ?? (await this.getParentEmail(userId));

      const { userSettings } = await this.appService.requestPermissionsForUser(orgEnvId, {
        userId,
        appId: app.id,
        permissions,
        parentEmail,
      });
      activationPermissions = SettingsService.transformSettingsToPermissions(userSettings, permissions);
    }

    await this.activationRepo.save({ user, app });

    await this.webhookService.sendChildActivated(
      {
        userId: Number(user.id),
        signUpCountry: user.signUpCountry ?? '',
        language: user.language,
        username: user.username,
        dateOfBirth: user.dateOfBirth?.toString(),
      },
      appId,
      orgEnvId,
    );

    await this.analyticsService.activationSuccess(user.signUpCountry ?? 'undefined', user.id, app.name, {
      language: user.language,
    });

    return { user, activationPermissions };
  }

  async verifyPassword(username: string, passwordToVerify: string, orgEnvId: string): Promise<boolean> {
    const user = await this.userRepo.findOneByOrFail({ username, orgEnvId });
    const userPassword = user.password;

    if (!userPassword) {
      throw new BadRequestException('User has no password');
    }

    const parts = userPassword.split('$');
    if (parts.length !== 4) {
      return false;
    }

    const [algorithm, iterationsStr, salt] = parts;
    const iterations = Number.parseInt(iterationsStr, 10);

    const supportedAlgorithms = ['pbkdf2_sha256', 'sha1', 'pbkdf2_sha1'];
    if (!supportedAlgorithms.includes(algorithm)) {
      return false;
    }

    let calculatedHash: string | null = null;

    switch (algorithm) {
      case 'pbkdf2_sha256': {
        calculatedHash = this.calculateKWSHash(passwordToVerify, salt, iterations);
        break;
      }
      case 'pbkdf2_sha1': {
        calculatedHash = this.calculatePopJamHash(passwordToVerify, salt, iterations);
        break;
      }
      case 'sha1': {
        if (iterations !== 1) {
          return false;
        }
        calculatedHash = this.calculateSha1Hash(passwordToVerify, salt);
        break;
      }
    }

    if (!calculatedHash) {
      return false;
    }

    const finalHash = `${algorithm}$${iterations}$${salt}$${calculatedHash}`;

    return finalHash === userPassword;
  }

  private calculateKWSHash(password: string, salt: string, iterations: number): string {
    const key = pbkdf2Sync(password, salt, iterations, 32, 'sha256');
    return key.toString('base64');
  }

  /**
   * Calculates a hash using PBKDF2 with SHA-1 algorithm (used for PopJam passwords) (Synchronous)
   */
  private calculatePopJamHash(password: string, salt: string, iterations: number): string {
    const key = pbkdf2Sync(password, salt, iterations, 64, 'sha1');
    return key.toString('base64').slice(0, 86).replaceAll('/', '_').replaceAll('+', '-');
  }

  /**
   * Calculates a SHA-1 hash
   */
  private calculateSha1Hash(password: string, salt: string): string {
    return crypto
      .createHash('sha1')
      .update(password + salt)
      .digest('hex')
      .toUpperCase();
  }

  async userActivatedForApp(userId: number, appId: number, orgEnvId: string) {
    const activation = await this.activationRepo.findOne({ where: { userId, orgEnvId, appId } });
    return activation !== null;
  }

  async isUsernameAvailable(username: string, orgEnvId: string): Promise<boolean> {
    if (!username || username.trim() === '') {
      return false;
    }

    const existingUser = await this.userRepo.findOne({
      where: {
        username: Raw((alias) => `LOWER(${alias}) = LOWER(:username)`, { username }),
        orgEnvId,
        deletedAt: IsNull(),
      },
    });

    return existingUser === null;
  }

  async createUserV2(body: UserRegisterDTO, signupCountry: string, orgEnvId: string) {
    const appInfo = await this.appService.getAppInfo(orgEnvId, body.originAppId);

    const { consentAge } = await this.ageGateService.getConsentAgeForCountry(
      {
        location: signupCountry as Tiso31662,
        dob: body.dateOfBirth,
      },
      appInfo.credentials,
    );

    const userAge = yearsSince(body.dateOfBirth);
    const isMinor = consentAge > userAge;
    if (!isMinor || userAge < 0) {
      throw new BadRequestException('User must be minor to register for an account or date of birth is invalid');
    }

    let passwordHash: string | undefined;
    if (body.password) {
      passwordHash = this.hashPassword(body.password);
    }

    const user = await this.userRepo.save({
      language: body.language as User['language'],
      signUpCountry: signupCountry as User['signUpCountry'],
      username: body.username,
      password: passwordHash,
      dateOfBirth: body.dateOfBirth,
      orgEnvId: orgEnvId,
    });

    return user.id;
  }

  private hashPassword(password: string) {
    const algorithm = 'pbkdf2_sha256';
    const iterations = 12000;
    const saltLength = 12;
    const salt = this.randomSalt(saltLength);
    const hash = this.calculateKWSHash(password, salt, iterations);

    return `${algorithm}$${iterations}$${salt}$${hash}`;
  }

  private randomSalt(saltLength: number) {
    return crypto.randomBytes(saltLength).toString('base64').slice(0, 12);
  }

  getUser(userId: number, orgEnvId: string) {
    return this.userRepo.findOne({
      where: { id: userId, orgEnvId },
    });
  }
}
