import {
  <PERSON>umn,
  CreateDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryColumn,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';

import { App } from '../app/app.entity';
import { OrgEnv } from '../org-env/org-env.entity';

@Entity()
export class Webhook {
  @PrimaryGeneratedColumn()
  id: number;

  @PrimaryColumn({
    type: 'character varying',
    // This must be the same as the "name" in the JoinColumn on app, due to a bug in TypeORM
    // that gets confused when you have two referenced columns with the same name
    name: 'orgEnvId',
  })
  orgEnvId: string;

  @ManyToOne(() => App, { onDelete: 'CASCADE', nullable: true, lazy: true })
  @JoinColumn([{ name: 'appId', referencedColumnName: 'id' }])
  @JoinColumn({ name: 'orgEnvId', referencedColumnName: 'orgEnvId' })
  app: App;

  @Column({ nullable: true })
  appId: number | null;

  @Column()
  name: string;

  @Column({ nullable: true })
  description?: string;

  @Column()
  url: string;

  @Column()
  secretKey: string;

  @ManyToOne(() => OrgEnv, { onDelete: 'CASCADE', lazy: true })
  @JoinColumn({ name: 'orgEnvId', referencedColumnName: 'id' })
  orgEnv: OrgEnv;

  @CreateDateColumn()
  createdAt?: Date;

  @UpdateDateColumn()
  updatedAt?: Date;
}
